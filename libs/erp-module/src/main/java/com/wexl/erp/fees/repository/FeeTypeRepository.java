package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.FeeType;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeTypeRepository extends JpaRepository<FeeType, Long> {
  List<FeeType> findAllByOrgSlugOrderByCreatedAtDesc(String orgSlug);

  Optional<FeeType> findByIdAndOrgSlug(UUID feeTypeId, String orgSlug);

  List<FeeType> findByOrgSlug(String orgSlug);
}
