
package com.wexl.erp.fees.controller;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.service.FeeDueReportService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/fee-due-reports")
public class FeeDueReportController {

  private final FeeDueReportService feeDueReportService;

  @IsOrgAdmin
  @PostMapping(value = "/csv", produces = "text/csv")
  public void generateFeeDueReportCsv(
      @PathVariable String orgSlug,
      @RequestBody FeeDto.FeeDueReportRequest request,
      HttpServletResponse httpServletResponse) {

    feeDueReportService.generateFeeDueReportCsv(orgSlug, request, httpServletResponse);
  }
}
