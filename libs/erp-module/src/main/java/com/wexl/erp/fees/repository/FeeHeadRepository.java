package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.*;
import com.wexl.retail.model.Student;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeHeadRepository extends JpaRepository<FeeHead, Long> {

  boolean existsByFeeMasterAndStudentAndFeeType(
      FeeMaster feeMaster, Student student, FeeType feeType);

  List<FeeHead> findAllByFeeMasterAndOrgSlug(FeeMaster feeMaster, String orgSlug);

  List<FeeHead> findAllByStudentIdAndOrgSlugAndStatusNotIn(
      Long studentId, String orgSlug, List<FeeStatus> status);

  Optional<FeeHead> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  List<FeeHead> findAllByConcession(Concession concession);

  Long countByFeeTypeIn(List<FeeType> feeTypes);

  Optional<FeeHead> findByIdAndOrgSlugAndStudent(UUID id, String orgSlug, Student student);

  @Query(
      "SELECT * FROM fee_heads fh "
          + "JOIN fh.fee_masters fm "
          + "JOIN fm.fee_groups fg "
          + "WHERE fh.org_slug = :orgSlug "
          + "AND fh.student_id IN (:studentIds) "
          + "AND fh.due_date < :currentDate "
          + "AND fh.due_date >= :fromDate "
          + "AND fh.due_date <= :toDate "
          + "AND fh.status IN ('UNPAID', 'PARTIALLY_PAID') "
          + "AND (:feeGroupId IS NULL OR fg.id = :feeGroupId) "
          + "ORDER BY fh.student_id, fh.due_date")
  List<FeeHead> findDueFeeHeads(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("currentDate") LocalDateTime currentDate,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate,
      @Param("feeGroupId") String feeGroupId);
}
